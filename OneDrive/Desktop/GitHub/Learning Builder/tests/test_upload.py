#!/usr/bin/env python
"""
Тестов скрипт за upload API endpoint.
"""
import requests
import os

def test_upload_api():
    """Тества upload API endpoint."""
    print("🧪 Тестване на upload API...")
    
    # Създаване на тестов файл (по-голям за да мине валидацията)
    test_content = """This is a test document for API testing. Python is a programming language.
Django is a web framework for Python that makes it easy to build web applications.
It includes many features like an ORM, admin interface, and URL routing.
Python is known for its simplicity and readability, making it a great choice for beginners.
Django follows the DRY (Don't Repeat Yourself) principle and encourages rapid development.
This document contains enough text to pass the minimum size validation requirements."""
    test_file_path = "test_upload.txt"
    
    with open(test_file_path, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    try:
        # Тестване на upload endpoint
        url = "http://127.0.0.1:8000/api/documents/upload/"
        
        with open(test_file_path, 'rb') as f:
            files = {'file': ('test_upload.txt', f, 'text/plain')}
            data = {'title': 'Test Document'}
            
            print(f"📤 Изпращане на POST заявка към {url}")
            response = requests.post(url, files=files, data=data)
            
            print(f"📊 Статус код: {response.status_code}")
            print(f"📄 Отговор: {response.text}")
            
            if response.status_code == 201:
                print("✅ Upload успешен!")
                return response.json()
            else:
                print("❌ Upload неуспешен!")
                return None
                
    except Exception as e:
        print(f"❌ Грешка: {e}")
        return None
    finally:
        # Изтриване на тестовия файл
        if os.path.exists(test_file_path):
            os.remove(test_file_path)

def test_documents_list():
    """Тества documents list endpoint."""
    print("\n🧪 Тестване на documents list API...")
    
    try:
        url = "http://127.0.0.1:8000/api/documents/"
        
        print(f"📤 Изпращане на GET заявка към {url}")
        response = requests.get(url)
        
        print(f"📊 Статус код: {response.status_code}")
        print(f"📄 Отговор: {response.text}")
        
        if response.status_code == 200:
            print("✅ Documents list успешен!")
            return response.json()
        else:
            print("❌ Documents list неуспешен!")
            return None
            
    except Exception as e:
        print(f"❌ Грешка: {e}")
        return None

if __name__ == "__main__":
    print("🔍 API Endpoint Testing")
    print("=" * 40)
    
    # Тест 1: Documents list
    documents = test_documents_list()
    
    # Тест 2: Upload
    uploaded_doc = test_upload_api()
    
    if uploaded_doc:
        print(f"\n📋 Качен документ ID: {uploaded_doc.get('id')}")
        
        # Тест 3: Documents list отново
        print("\n🔄 Проверка на documents list след upload...")
        documents_after = test_documents_list()
        
        if documents_after:
            print(f"📊 Брой документи: {len(documents_after)}")
    
    print("\n🏁 Тестването завърши!")
