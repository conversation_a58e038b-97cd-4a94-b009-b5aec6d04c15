#!/usr/bin/env python
"""
Test script to debug AI generation issues.
"""
import os
import sys
import django

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Learning_Builder.settings')
django.setup()

from documents.ai.client import OpenAIClient
from documents.ai.prompts import generate_flashcards_prompt, generate_questions_prompt
from documents.tasks import parse_flashcards, parse_questions

def test_ai_client():
    """Test basic AI client functionality."""
    print("Testing AI client...")
    
    try:
        client = OpenAIClient()
        print("✓ OpenAI client created successfully")
        
        # Test simple completion
        response = client.generate_completion("Say 'Hello World'", use_cache=False)
        print(f"✓ Simple completion works: {response[:50]}...")
        
        return True
    except Exception as e:
        print(f"✗ AI client error: {e}")
        return False

def test_flashcard_generation():
    """Test flashcard generation and parsing."""
    print("\nTesting flashcard generation...")
    
    try:
        client = OpenAIClient()
        test_text = "Python е програмен език. Django е web framework за Python."
        
        prompt = generate_flashcards_prompt(test_text)
        print(f"✓ Prompt generated: {prompt[:100]}...")
        
        ai_response = client.generate_completion(prompt, content_type='flashcards', use_cache=False)
        print(f"✓ AI response received: {ai_response[:100]}...")
        
        parsed = parse_flashcards(ai_response)
        print(f"✓ Parsed {len(parsed)} flashcards")
        
        for i, card in enumerate(parsed[:3]):  # Show first 3
            print(f"  Card {i+1}: {card['front']} -> {card['back']}")
        
        return True
    except Exception as e:
        print(f"✗ Flashcard generation error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_question_generation():
    """Test question generation and parsing."""
    print("\nTesting question generation...")
    
    try:
        client = OpenAIClient()
        test_text = "Python е програмен език. Django е web framework за Python."
        
        prompt = generate_questions_prompt(test_text)
        print(f"✓ Prompt generated: {prompt[:100]}...")
        
        ai_response = client.generate_completion(prompt, content_type='questions', use_cache=False)
        print(f"✓ AI response received: {ai_response[:100]}...")
        
        parsed = parse_questions(ai_response)
        print(f"✓ Parsed {len(parsed)} questions")
        
        for i, question in enumerate(parsed[:2]):  # Show first 2
            print(f"  Q{i+1}: {question['question_text']}")
            print(f"      Choices: {question['choices']}")
            print(f"      Correct: {question['correct_choice']}")
        
        return True
    except Exception as e:
        print(f"✗ Question generation error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_parser_robustness():
    """Test parser with various input formats."""
    print("\nTesting parser robustness...")
    
    # Test flashcard parser
    test_inputs = [
        "Python - Програмен език\nDjango - Web framework",
        '{"front": "API", "back": "Application Programming Interface"}',
        "Random text without proper format",
        ""
    ]
    
    for i, test_input in enumerate(test_inputs):
        try:
            result = parse_flashcards(test_input)
            print(f"✓ Flashcard test {i+1}: {len(result)} cards parsed")
        except Exception as e:
            print(f"✗ Flashcard test {i+1} failed: {e}")
    
    # Test question parser
    test_inputs = [
        "Какво е Python? | A) Змия | B) Език | C) Животно | D) Нищо | Верен отговор: B | Обяснение: Програмен език",
        "Random question text",
        ""
    ]
    
    for i, test_input in enumerate(test_inputs):
        try:
            result = parse_questions(test_input)
            print(f"✓ Question test {i+1}: {len(result)} questions parsed")
        except Exception as e:
            print(f"✗ Question test {i+1} failed: {e}")

if __name__ == "__main__":
    print("🔍 AI System Diagnostic Test")
    print("=" * 40)
    
    # Check environment
    openai_key = os.environ.get('OPENAI_API_KEY')
    if openai_key:
        print(f"✓ OpenAI API key found: {openai_key[:10]}...")
    else:
        print("✗ OpenAI API key not found in environment")
    
    # Run tests
    tests = [
        test_ai_client,
        test_parser_robustness,
        test_flashcard_generation,
        test_question_generation,
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed! AI system is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
