"""
Базови AI parsing класове за унификация на кода.
"""
import re
import json
import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Any


class BaseAIParser(ABC):
    """Базов клас за AI parsing с общи методи."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def log_parsing_start(self, content_type: str, ai_output: str):
        """Логва началото на parsing процеса."""
        self.logger.info(f'Parsing AI {content_type} output: {ai_output[:200]}...')
    
    def log_parsing_result(self, content_type: str, count: int):
        """Логва резултата от parsing."""
        self.logger.info(f'Successfully parsed {count} {content_type}')
    
    def try_json_parse(self, ai_output: str) -> List[Dict[str, Any]]:
        """Опитва JSON parsing на AI output."""
        try:
            # Почистване на output-а
            cleaned = ai_output.strip()
            if cleaned.startswith('```json'):
                cleaned = cleaned[7:]
            if cleaned.endswith('```'):
                cleaned = cleaned[:-3]
            cleaned = cleaned.strip()
            
            # Опит за парсване
            data = json.loads(cleaned)
            if isinstance(data, list):
                return data
            elif isinstance(data, dict) and 'items' in data:
                return data['items']
            else:
                return [data] if data else []
        except (json.JSONDecodeError, KeyError):
            return []
    
    def clean_text(self, text: str) -> str:
        """Почиства текст от нежелани символи."""
        if not text:
            return ""
        # Премахване на излишни whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        # Премахване на markdown форматиране
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)
        text = re.sub(r'\*(.*?)\*', r'\1', text)
        return text
    
    @abstractmethod
    def parse_line_format(self, ai_output: str) -> List[Dict[str, Any]]:
        """Парсва line-based формат. Трябва да се имплементира в наследниците."""
        pass
    
    @abstractmethod
    def validate_item(self, item: Dict[str, Any]) -> bool:
        """Валидира един item. Трябва да се имплементира в наследниците."""
        pass
    
    def parse(self, ai_output: str, content_type: str, limit: int = 30) -> List[Dict[str, Any]]:
        """Главен parsing метод с fallback стратегии."""
        self.log_parsing_start(content_type, ai_output)
        
        if not ai_output or not ai_output.strip():
            self.logger.warning(f'Empty AI output for {content_type}')
            return []
        
        results = []
        
        # Стратегия 1: JSON parsing
        json_results = self.try_json_parse(ai_output)
        if json_results:
            results = [item for item in json_results if self.validate_item(item)]
            if results:
                self.log_parsing_result(content_type, len(results))
                return results[:limit]
        
        # Стратегия 2: Line-based parsing
        line_results = self.parse_line_format(ai_output)
        if line_results:
            results = [item for item in line_results if self.validate_item(item)]
            if results:
                self.log_parsing_result(content_type, len(results))
                return results[:limit]
        
        # Ако нищо не работи
        self.logger.error(f'Failed to parse {content_type} from AI output')
        return []


class QuestionParser(BaseAIParser):
    """Parser за въпроси с избираем отговор."""
    
    def parse_line_format(self, ai_output: str) -> List[Dict[str, Any]]:
        """Парсва въпроси от line формат."""
        questions = []
        lines = [line.strip() for line in ai_output.split('\n') if line.strip()]
        
        for line in lines:
            # Търсене на pattern: Въпрос | A) ... | B) ... | C) ... | D) ... | Верен отговор: ... | Обяснение: ...
            if '|' in line and 'A)' in line and 'B)' in line:
                parts = [p.strip() for p in line.split('|')]
                if len(parts) >= 6:
                    question_text = parts[0]
                    choices = []
                    correct_choice = ""
                    explanation = ""
                    
                    # Извличане на choices
                    for part in parts[1:5]:
                        if part.startswith(('A)', 'B)', 'C)', 'D)')):
                            choices.append(part[2:].strip())
                    
                    # Извличане на верен отговор
                    for part in parts:
                        if part.startswith('Верен отговор:'):
                            correct_choice = part.replace('Верен отговор:', '').strip()
                        elif part.startswith('Обяснение:'):
                            explanation = part.replace('Обяснение:', '').strip()
                    
                    if question_text and len(choices) >= 4 and correct_choice:
                        questions.append({
                            'question_text': self.clean_text(question_text),
                            'choices': choices,
                            'correct_choice': self.clean_text(correct_choice),
                            'explanation': self.clean_text(explanation)
                        })
        
        return questions
    
    def validate_item(self, item: Dict[str, Any]) -> bool:
        """Валидира въпрос."""
        required_fields = ['question_text', 'choices', 'correct_choice']
        if not all(field in item for field in required_fields):
            return False
        
        if not item['question_text'] or len(item['question_text']) < 10:
            return False
        
        if not isinstance(item['choices'], list) or len(item['choices']) < 2:
            return False
        
        if not item['correct_choice']:
            return False
        
        return True


class FlashcardParser(BaseAIParser):
    """Parser за флашкарти."""
    
    def parse_line_format(self, ai_output: str) -> List[Dict[str, Any]]:
        """Парсва флашкарти от line формат."""
        flashcards = []
        lines = [line.strip() for line in ai_output.split('\n') if line.strip()]
        
        for line in lines:
            # Търсене на pattern: Въпрос - Отговор
            if ' - ' in line:
                parts = line.split(' - ', 1)
                if len(parts) == 2:
                    front = self.clean_text(parts[0])
                    back = self.clean_text(parts[1])
                    
                    if front and back:
                        flashcards.append({
                            'front': front,
                            'back': back
                        })
        
        return flashcards
    
    def validate_item(self, item: Dict[str, Any]) -> bool:
        """Валидира флашкарта."""
        required_fields = ['front', 'back']
        if not all(field in item for field in required_fields):
            return False
        
        if not item['front'] or len(item['front']) < 3:
            return False
        
        if not item['back'] or len(item['back']) < 3:
            return False
        
        return True


class ExplanationParser(BaseAIParser):
    """Parser за обяснения/термини."""
    
    def parse_line_format(self, ai_output: str) -> List[Dict[str, Any]]:
        """Парсва обяснения от line формат."""
        explanations = []
        lines = [line.strip() for line in ai_output.split('\n') if line.strip()]
        
        for line in lines:
            # Търсене на pattern: Термин: Обяснение
            if ':' in line:
                parts = line.split(':', 1)
                if len(parts) == 2:
                    term = self.clean_text(parts[0])
                    definition = self.clean_text(parts[1])
                    
                    # Премахване на префикси
                    term = re.sub(r'^(Термин|Term)[:\s]*', '', term, flags=re.IGNORECASE)
                    definition = re.sub(r'^(Дефиниция|Definition)[:\s]*', '', definition, flags=re.IGNORECASE)
                    
                    if term and definition and len(definition) > 5:
                        explanations.append({
                            'term': term,
                            'definition': definition
                        })
        
        return explanations
    
    def validate_item(self, item: Dict[str, Any]) -> bool:
        """Валидира обяснение."""
        required_fields = ['term', 'definition']
        if not all(field in item for field in required_fields):
            return False
        
        if not item['term'] or len(item['term']) < 2:
            return False
        
        if not item['definition'] or len(item['definition']) < 5:
            return False
        
        return True


# Singleton instances
question_parser = QuestionParser()
flashcard_parser = FlashcardParser()
explanation_parser = ExplanationParser()
